import { addItem, getCategories, ItemsUpdate, UploadImage, uploadImageToS3 } from "@/methods/cloths";
import { useQueryClient } from "@tanstack/react-query";
import * as ImagePicker from "expo-image-picker";
import { MinusIcon, PlusIcon } from "lucide-react-native";
import { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Image as ImageComponent,
  KeyboardAvoidingView,
  LayoutChangeEvent,
  ScrollView,
  TouchableOpacity,
  View
} from "react-native";
import useResponsive from "@/hooks/useResponsive";
import Modal from "react-native-modal";
import { SubtitleText, TitleSection } from "../common/Text";
import {
  AddClothsModalContainer,
  AddClothsModalHeader,
  AddClothsModalInput,
  CategoryItem,
  CategoryItemText,
  ClothesContainer,
  ClothsImageContainer,
  ColorText,
  HeaderText,
  HeaderTitle,
  QuantityButton,
  QuantityButtonContainer,
  QuantityContainer
} from "./styles";
// Import API-based categories
import { packingList as packingListString } from '@/constants/strings';
import { getCurrentUserGender } from "@/data/categories";
import { fetchCategoriesFromBackend } from "@/data/gender-categories";
import { getColorPresets } from "@/methods/preferences";
import { getUserProfile } from "@/methods/users";
import { capitalizeWords } from "@/utils/capitalizeWords";
import ColorsAndPatternsList from '../ColorsAndPatterns';
import { useTheme } from 'styled-components';
interface AddClothsCategoryModalProps {
  isVisible: boolean;
  onClose: () => void;
  item: any;
  selectedItem: any;
  packingList: any[];
  setIsModalVisible: (isVisible: boolean) => void;
  saveEditCloths: (
    note: string,
    quantity: number,
    parentId: string,
    itemId: string
  ) => void;
  removeItem: (selectedItem: any, item: any) => void;
  handleAddItemInPackingList: (item: any) => void;
}

export default function AddItemsModal({
  isVisible,
  onClose,
  item,
  selectedItem,
  packingList = [],
  saveEditCloths,
  removeItem,
  setIsModalVisible,
  handleAddItemInPackingList,
}: AddClothsCategoryModalProps) {
  // Initialize queryClient for cache invalidation
  const queryClient = useQueryClient();

  // Get responsive values
  const { isTablet, padding } = useResponsive();

  const [width, setWidth] = useState(0);
  const [quantity, setQuantity] = useState(item?.quantity || 1);
  const [selectedCategory, setSelectedCategory] = useState(
    item?.category || ""
  );
  const { data: categories } = getCategories();
  const { data: colorPresets } = getColorPresets();
  const {
    mutate: addItemMutation,
    isPending: isAddingItem,
    isSuccess: isItemAdded,
    data: itemData,
  } = addItem();
  const {
    mutate: uploadImageMutation,
    isPending: isUploadingImage,
    isSuccess: isImageUploaded,
    data: imageData,
  } = UploadImage();
  const {
    mutate: uploadImageToS3Mutation,
    isPending: isUploadingImageToS3,
    isSuccess: isImageUploadedToS3,
    data: imageDataToS3,
  } = uploadImageToS3();
  const {
    mutate: updateItemMutation,
    isPending: isUpdatingItem,
    isSuccess: isItemUpdated,
    data: itemUpdatedData,
  } = ItemsUpdate();

  const theme = useTheme();
  // State for gender-specific categories
  const [displayCategories, setDisplayCategories] = useState<any[]>([]);

  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();

  const [itemName, setItemName] = useState("");
  const [image, setImage] = useState("");
  const [displayImage, setDisplayImage] = useState("");
  const [brand, setBrand] = useState("");
  // color presets
  const [dominantColors, setDominantColors] = useState<any[]>([]);
  const [secondaryColor, setSecondaryColor] = useState<any[]>([]);
  // selected colors
  const [primarySelectedColor, setPrimarySelectedColor] = useState<any | null>(null);
  const [secondarySelectedColor, setSecondarySelectedColor] = useState<any | null>(null);
  // print and pattern presets
  const [printAndPattern, setPrintAndPattern] = useState<any[]>([]);
  // selected print and pattern
  const [selectedPrint, setSelectedPrint] = useState<any | null>(null);
  useEffect(() => {
    if (Array.isArray(colorPresets?.data.colorPresets)) {
      const colorPresetsData = colorPresets?.data.colorPresets;
      const dominantColors = colorPresetsData.find((p) => p._id === "dominant").items || [];
      const printAndPattern = colorPresetsData.find((p) => p._id === "printsAndPatterns").items || [];
      const secondaryColor = colorPresetsData.find((p) => p._id === "secondary").items || [];
      setDominantColors(dominantColors);
      setPrintAndPattern(printAndPattern);
      setSecondaryColor(secondaryColor);
    }
  }, [colorPresets?.data.colorPresets]);
  const pickImage = async () => {
    // No permissions request is necessary for launching the image library
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsEditing: false,
      base64: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      setImage(result.assets[0].base64 || "");
      setDisplayImage(result.assets[0].uri);
    }
  };

  useEffect(() => {
    if (imageDataToS3 && itemData?.itemId) {
      updateItemMutation(
        {
          itemId: itemData?.itemId,
          imageUrl: imageData?.fileURL,
        },
        {
          onSuccess: (response) => {
            console.log("imageDataToS3@@@@@", response);
          },
        }
      );
    }
  }, [isImageUploadedToS3]);

  useEffect(() => {
    if (isItemUpdated) {
      // Make sure we have a valid itemId
      if (!itemData?.itemId) {
        Alert.alert(
          "Error",
          "Failed to update item properly. Please try again."
        );
        return;
      }

      const item = {
        _id: itemData?.itemId,
        name: itemName,
        category: selectedCategory,
        color: primarySelectedColor?.name,
        brand: brand,
        quantity: quantity,
        imageUrl: imageData?.fileURL,
        printsAndPatterns: selectedPrint?.name,
        secondaryColor: secondarySelectedColor?.name,
      };

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["clothes"] });
      queryClient.invalidateQueries({ queryKey: ["item"] });
      queryClient.invalidateQueries({ queryKey: ["categories"] });

      // Force a refresh of the clothes data
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ["clothes"] });
      }, 500);

      // Add the item to the packing list
      handleAddItemInPackingList([item]);

      // Close the modal and reset the image
      onClose();
      setDisplayImage("");

      // Show success message
      Alert.alert("Success", "Item added successfully");
    }
  }, [isItemUpdated]);

  useEffect(() => {
    if (isImageUploaded) {
      const preSignedUrl = imageData?.preSignedURL;
      const fileType = imageData?.fileType;

      uploadImageToS3Mutation({
        imageUrl: image,
        preSignedUrl: preSignedUrl,
      });
    }
  }, [isImageUploaded]);

  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setWidth(width);
  };

  useEffect(() => {
    if (isItemAdded) {
      if (image) {
        uploadImageMutation({
          fileName: itemName,
          fileType: "image/jpeg",
          folderPath: "items",
          imageUrl: image,
        });
      } else {
        // Make sure we have a valid itemId
        if (!itemData?.itemId) {
          Alert.alert(
            "Error",
            "Failed to add item properly. Please try again."
          );
          return;
        }

        const item = {
          _id: itemData?.itemId,
          name: itemName,
          category: selectedCategory,
          color: primarySelectedColor?.name,
          brand: brand,
          quantity: quantity,
          imageUrl: "",
          printsAndPatterns: selectedPrint?.name,
          secondaryColor: secondarySelectedColor?.name,
        };

        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ["clothes"] });
        queryClient.invalidateQueries({ queryKey: ["item"] });
        queryClient.invalidateQueries({ queryKey: ["categories"] });

        // Force a refresh of the clothes data
        setTimeout(() => {
          queryClient.refetchQueries({ queryKey: ["clothes"] });
        }, 500);

        // Add the item to the packing list
        handleAddItemInPackingList([item]);

        // Close the modal
        onClose();
      }
      setPrimarySelectedColor(null);
      setSecondarySelectedColor(null);
      setSelectedPrint(null);

    }
  }, [isItemAdded]);

  useEffect(() => {
    setItemName("");
    setSelectedCategory("");
    setQuantity(1);
    setPrimarySelectedColor(null);
    setSecondarySelectedColor(null);
    setSelectedPrint(null);
    setBrand("");
  }, [isVisible]);

  // Load gender-specific categories
  useEffect(() => {
    const loadGenderCategories = async () => {
      try {
        // Get gender from user profile
        let gender = null;
        if (userProfile?.data?.profile?.gender) {
          gender = userProfile.data.profile.gender;
        } else {
          // Try to get gender from Meteor.user()
          gender = getCurrentUserGender();
        }

        if (!gender) {
          // Use fallback categories
          const fallbackCategories = [
            { _id: "tops", name: "Tops", category: "Tops" },
            { _id: "bottoms", name: "Bottoms", category: "Bottoms" },
            { _id: "dresses", name: "Dresses", category: "Dresses" },
            { _id: "shoes", name: "Shoes", category: "Shoes" },
            {
              _id: "accessories",
              name: "Accessories",
              category: "Accessories",
            },
          ];
          setDisplayCategories(fallbackCategories);
          return;
        }

        // Get categories from backend based on gender
        const backendCategories = await fetchCategoriesFromBackend(gender);

        // Create a Map to track categories by name for deduplication
        const categoryMap = new Map<string, any>();

        // Collect categories by name, keeping only the first occurrence
        // Make sure backendCategories is an array before iterating
        if (!Array.isArray(backendCategories)) {
          console.error(
            "AddItems - backendCategories is not an array:",
            backendCategories
          );
          return; // Exit early if not an array
        }

        backendCategories.forEach((category) => {
          // Skip if category is undefined or null or has no name
          if (!category || !category.name) {
            return;
          }

          if (!categoryMap.has(category.name)) {
            categoryMap.set(category.name, category);
          } else {
            console.log(
              `AddItems - Skipping duplicate category: ${category.name}`
            );
          }
        });

        // Format categories for display - include all top-level categories (after deduplication)
        const formattedCategories = Array.from(categoryMap.values())
          .filter(
            (category) =>
              category !== null && category !== undefined && category.name
          ) // Filter out null/undefined and categories with no name
          .map((category) => ({
            _id:
              category.id ||
              `category-${category.name.toLowerCase().replace(/\s+/g, "-")}`,
            name: category.name,
            category: category.name,
          }));

        // Make sure we have all the basic categories at minimum
        const basicCategories = [
          "Tops",
          "Bottoms",
          "Dresses",
          "Shoes",
          "Accessories",
        ];

        // Check if each basic category exists in the formatted categories
        basicCategories.forEach((basicCat) => {
          if (!formattedCategories.some((cat) => cat.name === basicCat)) {
            // Add the missing basic category
            formattedCategories.push({
              _id: `basic-${basicCat.toLowerCase()}`,
              name: basicCat,
              category: basicCat,
            });
          }
        });

        setDisplayCategories(formattedCategories);
      } catch (error) {
        console.error("Error loading gender-specific categories:", error);
        // Fallback to a comprehensive set of categories if there's an error
        const fallbackCategories = [
          { _id: "tops", name: "Tops", category: "Tops" },
          { _id: "bottoms", name: "Bottoms", category: "Bottoms" },
          { _id: "dresses", name: "Dresses", category: "Dresses" },
          {
            _id: "matching-sets",
            name: "Matching Sets",
            category: "Matching Sets",
          },
          { _id: "outerwear", name: "Outerwear", category: "Outerwear" },
          { _id: "swimwear", name: "Swimwear", category: "Swimwear" },
          { _id: "activewear", name: "Activewear", category: "Activewear" },
          { _id: "shoes", name: "Shoes", category: "Shoes" },
          { _id: "jewellery", name: "Jewellery", category: "Jewellery" },
          { _id: "bags", name: "Bags", category: "Bags" },
          { _id: "accessories", name: "Accessories", category: "Accessories" },
          { _id: "tech", name: "Tech", category: "Tech" },
          { _id: "others", name: "Others", category: "Others" },
        ];
        setDisplayCategories(fallbackCategories);
      }
    };

    loadGenderCategories();
  }, [userProfile, categories]);

  const saveItem = () => {
    // Validate item name
    if (!itemName || itemName.trim() === "") {
      Alert.alert("Please enter an item name");
      return;
    }

    // Validate category
    if (!selectedCategory) {
      Alert.alert(
        "Please select a category",
        "Select a category in your Myuse closet to add this item"
      );
      return;
    }

    if (!selectedCategory._id) {
      Alert.alert("Error", "Invalid category selected. Please try again.");
      return;
    }

    if (!primarySelectedColor?._id) {
      Alert.alert("Please select a primary color");
      return;
    }

    // Prepare the data for the API call
    const itemData = {
      name: itemName.trim(),
      itemCategoryId: selectedCategory._id,
      color: primarySelectedColor?.name,
      brand: brand || "",
      secondaryColor: secondarySelectedColor?.name || "",
      printsAndPatterns: selectedPrint?.name || "",
    };
    try {
      // Clear any existing clothes cache before adding the item
      queryClient.invalidateQueries({ queryKey: ["clothes"] });

      // Add the item
      addItemMutation(itemData);
    } catch (error) {
      Alert.alert("Error", "Failed to add item. Please try again.");
    }
  };
  return (
    <Modal
      key={item?.id}
      style={{ justifyContent: "flex-end", margin: 0 }}
      isVisible={isVisible}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
    >
      <KeyboardAvoidingView
        behavior="padding"
        style={{ flex: 1, justifyContent: "flex-end" }}
      >
        <AddClothsModalContainer>
          {isAddingItem ||
            isUploadingImage ||
            isUploadingImageToS3 ||
            isUpdatingItem ? (
            <ActivityIndicator size={400} color={theme.brand.green[500]} />
          ) : (
            <View>
              <AddClothsModalHeader>
                <TouchableOpacity style={{ flex: 1 }} onPress={onClose}>
                  <HeaderText>{packingListString.cancel}</HeaderText>
                </TouchableOpacity>
                <View style={{ flex: 3, alignItems: "center" }}>
                  <HeaderTitle>
                    {packingListString.title}
                  </HeaderTitle>
                </View>
                <View style={{ flex: 1, alignItems: "flex-end" }}>
                  <TouchableOpacity
                    onPress={() => {
                      saveItem();
                    }}
                  >
                    <HeaderText>{packingListString.save}</HeaderText>
                  </TouchableOpacity>
                </View>
              </AddClothsModalHeader>
              <ScrollView
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                bounces={false}
                contentContainerStyle={{
                  paddingBottom: isTablet ? padding.MODAL_CONTENT_HORIZONTAL * 2 : padding.MODAL_CONTENT_HORIZONTAL * 1.5
                }}
              >
                <View style={{ marginTop: 20 }}>
                  <TitleSection string={packingListString.itemName} />
                  <AddClothsModalInput
                    placeholder="Item Name"
                    value={itemName}
                    onChangeText={setItemName}
                    onBlur={() => setItemName(capitalizeWords(itemName))}
                  />
                </View>
                <View style={{ marginTop: 20 }}>
                  <TitleSection string={packingListString.brand} />
                  <AddClothsModalInput
                    placeholder="Brand name"
                    value={brand}
                    onChangeText={setBrand}
                    onBlur={() => setBrand(capitalizeWords(brand))}
                  />
                </View>
                <View style={{ marginTop: 20 }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                    <TitleSection string={`${packingListString.color}:`} />
                    <ColorText>
                      {primarySelectedColor?.name}
                      {secondarySelectedColor &&
                        <ColorText style={{ marginLeft: 0 }}>
                          ,{secondarySelectedColor?.name}
                        </ColorText>}
                    </ColorText>
                  </View>
                  {/* colors */}
                  <ColorsAndPatternsList
                    colorPatternData={dominantColors}
                    secondaryColor={secondaryColor}
                    isColor
                    numColumns={6}
                    lengthToDisplay={12}
                    showViewMoreButton
                    setPrimarySelectedColor={setPrimarySelectedColor}
                    primarySelectedColor={primarySelectedColor}
                    setSecondarySelectedColor={setSecondarySelectedColor}
                    secondarySelectedColor={secondarySelectedColor}
                  />
                </View>
                <View style={{ marginTop: 20 }}>
                  <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                    <TitleSection string={`${packingListString.printsPatterns}:`} />
                    <ColorText>{selectedPrint?.name}</ColorText>
                  </View>
                  {/* patterns */}
                  <ColorsAndPatternsList
                    colorPatternData={printAndPattern}
                    isPattern
                    numColumns={6}
                    lengthToDisplay={12}
                    showViewMoreButton
                    setPrintPattern={setSelectedPrint}
                    selectedPrint={selectedPrint}
                  />
                </View>
                <View style={{ marginTop: 20 }}>
                  <TitleSection string={packingListString.category} />
                  <FlatList
                    style={{ marginTop: 10 }}
                    data={displayCategories}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    renderItem={({ item }) => {
                      return (
                        <CategoryItem
                          key={item._id || item.id}
                          isSelected={selectedCategory.name === item.name}
                          onPress={() => setSelectedCategory(item)}
                        >
                          <CategoryItemText
                            isSelected={selectedCategory.name === item.name}
                          >
                            {item.name}
                          </CategoryItemText>
                        </CategoryItem>
                      );
                    }}
                  />
                </View>

                <QuantityContainer>
                  <TitleSection string={packingListString.quantity} />
                  <QuantityButtonContainer>
                    <QuantityButton
                      style={{ backgroundColor: theme.brand.green[100] }}
                      onPress={() => setQuantity(quantity - 1)}
                    >
                      <MinusIcon color={theme.brand.green[500]} />
                    </QuantityButton>
                    <SubtitleText string={`${quantity}`} />
                    <QuantityButton
                      style={{ backgroundColor: theme.brand.green[500] }}
                      onPress={() => setQuantity(quantity + 1)}
                    >
                      <PlusIcon color={theme.brand.gray[50]} />
                    </QuantityButton>
                  </QuantityButtonContainer>
                </QuantityContainer>
                <View style={{ marginTop: 20 }}>
                  <TitleSection string={packingListString.image} />
                </View>
                <View style={{
                  marginTop: 20,
                  marginBottom: isTablet ? padding.MODAL_CONTENT_HORIZONTAL : padding.MODAL_CONTENT_HORIZONTAL * 0.75
                }}>
                  <ClothsImageContainer onPress={pickImage} width={width}>
                    {displayImage ? (
                      <ImageComponent
                        source={{ uri: displayImage }}
                        style={{ width: "100%", height: "100%" }}
                      />
                    ) : (
                      <PlusIcon color={theme.brand.gray[50]} />
                    )}
                  </ClothsImageContainer>
                </View>
                <ClothesContainer
                  onLayout={handleLayout}
                  style={{ marginTop: 20 }}
                />
              </ScrollView>
            </View>
          )}
        </AddClothsModalContainer>
      </KeyboardAvoidingView>
    </Modal>
  );
}
